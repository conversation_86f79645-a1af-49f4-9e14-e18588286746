# CS2 DMA Cheat

Open source, do not forget to put your ⭐



go here: https://github.com/Enoouo/Pro-CS2_DMA

## Installation

Firstly you need to download exe / build it from sources. In same folder drop vmm.dll, leechcore.dll, FTD3XX.dll. After that you need get
- [offsets.json](https://github.com/a2x/cs2-dumper/blob/main/generated/offsets.json)
- [client.dll.json](https://github.com/a2x/cs2-dumper/blob/main/generated/client.dll.json)

After that we need to setup KmBox settings in kmbox.json

Example for net
```json
{
   "type":"net",
   "ip":"************",
   "port":7777,
   "uuid":"56X21D14"
}
```
Example for b
```json
{
   "type":"b"
}
```
KmBox B has no tests, so if you have problem - you can write me and I will fix it.

To hide menu - press F8
    
## Features

- **Visuals**
* ESP: Box, eye ray, health bar, weapon, distance, name
* Lines to enemy
- **AimBot**
* <PERSON><PERSON>, Aimfov, Visual Check, Smooth, IgnoreOnShoot
* AutoShot, Aim bone for (default, pistols, snipers)
- **Radar**
* Size (small, big)
- **TriggerBot**
* Hotkey, mode (always, onkey), delay
- **Utilities**
* Team Check
- **Configs**
* Create, Save, Load, Delete
## Screenshots

![](https://i.ibb.co/TbzPQ1Q/image.png)

## Contacts

 - [Telegram](https://t.me/cherepoveciv)
 - [GitHub](https://github.com/IvanAcoola)


## Credits

- [@CS2_DMA_Extrnal](https://github.com/Mzzzj/CS2_DMA_Extrnal)
- [@KmBoxNet](https://github.com/TKazer/KmBoxNetManager)
- [@KmBoxB](https://github.com/sys-1337/kmbox-communication)

