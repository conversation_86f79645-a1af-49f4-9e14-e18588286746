﻿  Bone.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Bone.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Bone.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Bone.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  Cheats.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(21,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(22,25): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(43,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(44,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(49,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(53,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(54,59): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(55,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,77): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,64): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,72): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,63): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,59): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(14,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(37,46): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(64,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(65,19): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(67,70): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(107,20): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(109,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(110,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(111,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(211,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(243,34): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(295,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Render.hpp(327,34): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(311,225): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(349,52): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(353,61): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(371,60): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(375,78): warning C4305: 'argument': truncation from 'double' to 'float'
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(393,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(393,43): warning C4244: 'argument': conversion from 'DWORD64' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(414,199): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Cheats.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  CheatsThread.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(21,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(22,25): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(43,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(44,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(49,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(53,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(54,59): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(55,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,77): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,64): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,72): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,63): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,59): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  Entity.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Entity.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Entity.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Entity.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  Game.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Game.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Game.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Game.cpp(144,99): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Game.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  GlobalVars.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'GlobalVars.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'GlobalVars.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  KmboxB.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KMbox\kmbox_b.hpp(83,57): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'KMbox/KmboxB.cpp')
  
  KmBoxNetManager.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'KMbox/KmBoxNetManager.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxNetManager.cpp(32,12): warning C4244: 'argument': conversion from 'time_t' to 'unsigned int', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxNetManager.cpp(40,38): warning C4244: '=': conversion from '__int64' to 'unsigned int', possible loss of data
  main.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KmBoxData.h(32,37): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(21,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(22,25): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(43,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(44,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(49,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(53,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(54,59): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(55,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,77): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,64): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,72): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,63): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,59): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\main.cpp(87,78): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\main.cpp(87,47): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'main.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  Offsets.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Offsets.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Offsets.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Offsets.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(17,87): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(18,83): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(19,86): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(20,109): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(21,97): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(22,85): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(23,87): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(26,81): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(27,82): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(28,94): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(29,96): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(30,103): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(33,85): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(34,87): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(35,88): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(36,95): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(37,102): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(38,98): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(39,114): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(40,104): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(41,96): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(42,104): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(43,96): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(44,96): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(45,96): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(46,84): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(47,101): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(48,102): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(49,80): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(50,27): warning C4244: 'initializing': conversion from 'uint64_t' to 'int', possible loss of data
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(51,23): warning C4244: 'initializing': conversion from 'uint64_t' to 'int', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Offsets.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx11.cpp
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  OS-ImGui.cpp
  OS-ImGui_Base.cpp
  OS-ImGui_External.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=std::+::_Size_type,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=std::+::_Size_type
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=std::+::_Size_type,
              _InIt=wchar_t *,
              _SizeTy=std::+::_Size_type
          ]
  
  Generating Code...
  Compiling...
  Radar.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Radar/Radar.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Radar/Radar.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(2,10): error C1083: Cannot open include file: 'D3DX11tex.h': No such file or directory
  TriggerBot.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9):
      see previous definition of 'KEY_EXECUTE'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'TriggerBot.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'TriggerBot.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  ConfigMenu.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(21,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(22,25): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(43,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(44,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(49,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(53,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(54,59): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(55,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,77): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,64): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,72): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,63): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,59): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  ConfigSaver.cpp
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(21,29): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(22,25): warning C4305: 'initializing': truncation from 'double' to 'float'
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(43,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(44,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(49,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(53,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(54,59): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(55,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,77): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,64): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,72): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,63): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,59): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              C:\Users\<USER>\OneDrive\Desktop\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  Generating Code...
