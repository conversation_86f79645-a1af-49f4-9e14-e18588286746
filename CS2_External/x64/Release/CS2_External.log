﻿  Bone.cpp
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Bone.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Bone.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Bone.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Bone.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Bone.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  Cheats.cpp
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Cheats.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'Cheats.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(43,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(44,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(49,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(53,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(54,59): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(55,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,77): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,64): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,72): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,63): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,59): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(14,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(37,46): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(64,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(65,19): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(67,70): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(107,20): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(109,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(110,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(111,23): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(211,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(243,34): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(295,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Render.hpp(327,34): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Cheats.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(311,225): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(393,69): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(393,43): warning C4244: 'argument': conversion from 'DWORD64' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Cheats.cpp(414,199): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Cheats.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  CheatsThread.cpp
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(43,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(44,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(49,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(53,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(54,59): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(55,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,77): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,64): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,72): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,63): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,59): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'CheatsThread.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'CheatsThread.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  Entity.cpp
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Entity.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Entity.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Entity.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Entity.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Entity.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  Game.cpp
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Game.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Game.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Game.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Game.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\Game.cpp(144,99): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Game.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  GlobalVars.cpp
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'GlobalVars.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'GlobalVars.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'GlobalVars.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  KmboxB.cpp
R:\CS2-DMA-Cheat-main\CS2_External\KMbox\kmbox_b.hpp(83,57): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'KMbox/KmboxB.cpp')
  
  KmBoxNetManager.cpp
R:\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'KMbox/KmBoxNetManager.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
R:\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxNetManager.cpp(32,12): warning C4244: 'argument': conversion from 'time_t' to 'unsigned int', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxNetManager.cpp(40,38): warning C4244: '=': conversion from '__int64' to 'unsigned int', possible loss of data
  main.cpp
R:\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
R:\CS2-DMA-Cheat-main\CS2_External\KmBoxData.h(32,37): warning C4244: '=': conversion from 'int64_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'main.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(43,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(44,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(49,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(53,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(54,59): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(55,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,77): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,64): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,72): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,63): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,59): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\main.cpp(87,78): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\main.cpp(87,47): warning C4244: 'argument': conversion from 'int' to 'float', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'main.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  Offsets.cpp
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Offsets.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Offsets.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Offsets.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(17,87): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(18,83): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(19,86): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(20,109): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(21,97): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(22,85): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(23,87): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(26,81): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(27,82): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(28,94): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(29,96): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(30,103): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(33,85): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(34,87): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(35,88): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(36,95): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(37,102): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(38,98): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(39,114): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(40,104): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(41,96): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(42,104): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(43,96): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(44,96): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(45,96): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(46,84): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(47,101): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(48,102): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(49,80): warning C4244: '=': conversion from 'uint64_t' to 'DWORD', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(50,27): warning C4244: 'initializing': conversion from 'uint64_t' to 'int', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Offsets.cpp(51,23): warning C4244: 'initializing': conversion from 'uint64_t' to 'int', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Offsets.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  imgui.cpp
  imgui_demo.cpp
  imgui_draw.cpp
  imgui_impl_dx11.cpp
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/imgui/imgui_impl_dx11.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
  imgui_impl_win32.cpp
  imgui_tables.cpp
  imgui_widgets.cpp
  OS-ImGui.cpp
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
  OS-ImGui_Base.cpp
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_Base.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
  OS-ImGui_External.cpp
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'OS-ImGui/OS-ImGui_External.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=std::+::_Size_type,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=std::+::_Size_type
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=std::+::_Size_type,
              _InIt=wchar_t *,
              _SizeTy=std::+::_Size_type
          ]
  
  Compiling...
  Radar.cpp
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Radar/Radar.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Radar/Radar.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(131,23): warning C4244: '=': conversion from '_Ty1' to 'float', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(131,23): warning C4244:         with
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(131,23): warning C4244:         [
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(131,23): warning C4244:             _Ty1=int
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(131,23): warning C4244:         ]
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(132,23): warning C4244: '=': conversion from '_Ty2' to 'float', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(132,23): warning C4244:         with
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(132,23): warning C4244:         [
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(132,23): warning C4244:             _Ty2=int
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(132,23): warning C4244:         ]
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(163,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(167,25): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(168,25): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(170,26): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(171,26): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244: 'initializing': conversion from '_Ty' to '_Ty1', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:             _Ty=float
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         and
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:             _Ty1=int
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         ]
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(72,29):
          see reference to function template instantiation 'std::pair<int,int>::pair<float,float,0>(std::pair<float,float> &&) noexcept' being compiled
              R:\CS2-DMA-Cheat-main\CS2_External\Radar\Radar.cpp(72,29):
              see the first reference to 'std::pair<int,int>::pair' in 'Base_Radar::world_to_minimap2'
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244: 'initializing': conversion from '_Ty' to '_Ty2', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:             _Ty=float
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         and
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:             _Ty2=int
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         ]
  (compiling source file 'Radar/Radar.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244: 'initializing': conversion from '_Ty' to '_Ty2', possible loss of data
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:             _Ty=int
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         and
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:             _Ty2=float
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,61): warning C4244:         ]
  (compiling source file 'Radar/Radar.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Radar/Radar.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  TriggerBot.cpp
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9):
      see previous definition of 'KEY_EXECUTE'
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'TriggerBot.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'TriggerBot.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'TriggerBot.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  ConfigMenu.cpp
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(43,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(44,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(49,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(53,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(54,59): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(55,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,77): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,64): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,72): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,63): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,59): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Utils/ConfigMenu.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  ConfigSaver.cpp
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(122,11):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(489,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\leechcore.h(498,10):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(392,35):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1454,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1464,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1471,27):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1480,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1489,36):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1505,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1515,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1524,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1532,31):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1539,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1548,28):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1559,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1568,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1576,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1585,26):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1594,24):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(1603,29):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25): warning C4200: nonstandard extension used: zero-sized array in struct/union
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\includes\vmmdll.h(2138,25):
      This member will be ignored by a defaulted constructor or copy/move assignment operator
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(13,9): warning C4005: '_is_invalid': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(12,9):
      see previous definition of '_is_invalid'
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(175,45): warning C4267: 'argument': conversion from 'size_t' to 'DWORD', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(344,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(12,9): warning C4005: 'DXGI_STATUS_OCCLUDED': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59020,9):
      see previous definition of 'DXGI_STATUS_OCCLUDED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(13,9): warning C4005: 'DXGI_STATUS_CLIPPED': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59029,9):
      see previous definition of 'DXGI_STATUS_CLIPPED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(14,9): warning C4005: 'DXGI_STATUS_NO_REDIRECTION': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59038,9):
      see previous definition of 'DXGI_STATUS_NO_REDIRECTION'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(15,9): warning C4005: 'DXGI_STATUS_NO_DESKTOP_ACCESS': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59047,9):
      see previous definition of 'DXGI_STATUS_NO_DESKTOP_ACCESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(16,9): warning C4005: 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59056,9):
      see previous definition of 'DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(17,9): warning C4005: 'DXGI_STATUS_MODE_CHANGED': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59065,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(18,9): warning C4005: 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59074,9):
      see previous definition of 'DXGI_STATUS_MODE_CHANGE_IN_PROGRESS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(21,9): warning C4005: 'DXGI_ERROR_INVALID_CALL': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59089,9):
      see previous definition of 'DXGI_ERROR_INVALID_CALL'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(22,9): warning C4005: 'DXGI_ERROR_NOT_FOUND': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59098,9):
      see previous definition of 'DXGI_ERROR_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(23,9): warning C4005: 'DXGI_ERROR_MORE_DATA': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59107,9):
      see previous definition of 'DXGI_ERROR_MORE_DATA'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(24,9): warning C4005: 'DXGI_ERROR_UNSUPPORTED': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59116,9):
      see previous definition of 'DXGI_ERROR_UNSUPPORTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(25,9): warning C4005: 'DXGI_ERROR_DEVICE_REMOVED': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59125,9):
      see previous definition of 'DXGI_ERROR_DEVICE_REMOVED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(26,9): warning C4005: 'DXGI_ERROR_DEVICE_HUNG': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59134,9):
      see previous definition of 'DXGI_ERROR_DEVICE_HUNG'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(27,9): warning C4005: 'DXGI_ERROR_DEVICE_RESET': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59144,9):
      see previous definition of 'DXGI_ERROR_DEVICE_RESET'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(28,9): warning C4005: 'DXGI_ERROR_WAS_STILL_DRAWING': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59153,9):
      see previous definition of 'DXGI_ERROR_WAS_STILL_DRAWING'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(29,9): warning C4005: 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59163,9):
      see previous definition of 'DXGI_ERROR_FRAME_STATISTICS_DISJOINT'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(30,9): warning C4005: 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59172,9):
      see previous definition of 'DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(31,9): warning C4005: 'DXGI_ERROR_DRIVER_INTERNAL_ERROR': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59182,9):
      see previous definition of 'DXGI_ERROR_DRIVER_INTERNAL_ERROR'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(32,9): warning C4005: 'DXGI_ERROR_NONEXCLUSIVE': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59191,9):
      see previous definition of 'DXGI_ERROR_NONEXCLUSIVE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(33,9): warning C4005: 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59200,9):
      see previous definition of 'DXGI_ERROR_NOT_CURRENTLY_AVAILABLE'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(34,9): warning C4005: 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59210,9):
      see previous definition of 'DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\dxgitype.h(35,9): warning C4005: 'DXGI_ERROR_REMOTE_OUTOFMEMORY': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59219,9):
      see previous definition of 'DXGI_ERROR_REMOTE_OUTOFMEMORY'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(917,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59534,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(918,9): warning C4005: 'D3D11_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59543,9):
      see previous definition of 'D3D11_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(919,9): warning C4005: 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59553,9):
      see previous definition of 'D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d11.h(920,9): warning C4005: 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59562,9):
      see previous definition of 'D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(608,9): warning C4005: 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59510,9):
      see previous definition of 'D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS'
  
R:\CS2-DMA-Cheat-main\CS2_External\SDK\Include\d3d10.h(609,9): warning C4005: 'D3D10_ERROR_FILE_NOT_FOUND': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\winerror.h(59519,9):
      see previous definition of 'D3D10_ERROR_FILE_NOT_FOUND'
  
R:\CS2-DMA-Cheat-main\CS2_External\KMbox\KmBoxConfig.h(213,9): warning C4005: 'KEY_EXECUTE': macro redefinition
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\winnt.h(24543,9):
      see previous definition of 'KEY_EXECUTE'
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(43,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(44,27): warning C4244: 'initializing': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(49,24): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(53,57): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(54,59): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(55,20): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,89): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,77): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(103,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,85): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,73): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(104,64): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,72): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(107,63): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,68): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
R:\CS2-DMA-Cheat-main\CS2_External\AimBot.hpp(108,59): warning C4244: 'argument': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Utils/ConfigSaver.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      the template instantiation context (the oldest one first) is
          R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,21):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              R:\CS2-DMA-Cheat-main\CS2_External\Utils\ProcessManager.hpp(311,3):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'ProcessManager::QueryValue'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  Generating code
  3912 of 3944 functions (99.2%) were compiled, the rest were copied from previous compilation.
    78 functions were new in current compilation
    2 functions had inline decision re-evaluated but remain unchanged
  Finished generating code
  CS2_External.vcxproj -> R:\CS2-DMA-Cheat-main\x64\Release\cs2.exe
  'pwsh.exe' is not recognized as an internal or external command,
  operable program or batch file.
