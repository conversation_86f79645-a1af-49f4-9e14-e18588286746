<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{82688835-fba1-418a-9ad2-9a2d94c0fd47}</ProjectGuid>
    <RootNamespace>CS2External</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>cs2</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>$(ProjectDir)includes;$(IncludePath)</IncludePath>
    <ExternalIncludePath>$(ProjectDir)includes;$(ExternalIncludePath)</ExternalIncludePath>
    <ReferencePath>$(ProjectDir)includes;$(ReferencePath)</ReferencePath>
    <LibraryPath>$(ProjectDir)includes;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>$(ProjectDir)includes;$(IncludePath)</IncludePath>
    <ExternalIncludePath>$(ProjectDir)includes;$(ExternalIncludePath)</ExternalIncludePath>
    <ReferencePath>$(ProjectDir)includes;$(ReferencePath)</ReferencePath>
    <LibraryPath>$(ProjectDir)includes;$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;NOMINMAX;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <AdditionalIncludeDirectories>$(ProjectDir)includes;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4996</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <AdditionalDependencies>leechcore.lib;vmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(ProjectDir)includes;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;NOMINMAX;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <EnableEnhancedInstructionSet>AdvancedVectorExtensions2</EnableEnhancedInstructionSet>
      <FloatingPointModel>Fast</FloatingPointModel>
      <DisableSpecificWarnings>4996</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>$(ProjectDir)SDK\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(ProjectDir)SDK\Lib\x64;$(ProjectDir)includes;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>leechcore.lib;vmm.lib;D3DX11.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="Bone.h" />
    <ClInclude Include="Aimbot.hpp" />
    <ClInclude Include="Cheats.h" />
    <ClInclude Include="CheatsThread.h" />
    <ClInclude Include="Entity.h" />
    <ClInclude Include="Game.h" />
    <ClInclude Include="Globals.hpp" />
    <ClInclude Include="GlobalVars.h" />
    <ClInclude Include="KmBoxData.h" />
    <ClInclude Include="KMbox\ExcludedH.h" />
    <ClInclude Include="KMbox\KmboxB.h" />
    <ClInclude Include="KMbox\KmBoxConfig.h" />
    <ClInclude Include="KMbox\KmBoxNetManager.h" />
    <ClInclude Include="KMbox\kmbox_b.hpp" />
    <ClInclude Include="mapsdata.h" />
    <ClInclude Include="MenuConfig.hpp" />
    <ClInclude Include="Offsets.h" />
    <ClInclude Include="OS-ImGui\imgui\imconfig.h" />
    <ClInclude Include="OS-ImGui\imgui\imgui.h" />
    <ClInclude Include="OS-ImGui\imgui\imgui_impl_dx11.h" />
    <ClInclude Include="OS-ImGui\imgui\imgui_impl_win32.h" />
    <ClInclude Include="OS-ImGui\imgui\imgui_internal.h" />
    <ClInclude Include="OS-ImGui\imgui\imstb_rectpack.h" />
    <ClInclude Include="OS-ImGui\imgui\imstb_textedit.h" />
    <ClInclude Include="OS-ImGui\imgui\imstb_truetype.h" />
    <ClInclude Include="OS-ImGui\OS-ImGui.h" />
    <ClInclude Include="OS-ImGui\OS-ImGui_Base.h" />
    <ClInclude Include="OS-ImGui\OS-ImGui_Exception.hpp" />
    <ClInclude Include="OS-ImGui\OS-ImGui_External.h" />
    <ClInclude Include="OS-ImGui\OS-ImGui_Struct.h" />
    <ClInclude Include="Radar\Radar.h" />
    <ClInclude Include="Render.hpp" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="stb_image.h" />
    <ClInclude Include="TriggerBot.h" />
    <ClInclude Include="Utils\ConfigMenu.hpp" />
    <ClInclude Include="Utils\ConfigSaver.hpp" />
    <ClInclude Include="Utils\Format.hpp" />
    <ClInclude Include="Utils\ProcessManager.hpp" />
    <ClInclude Include="View.hpp" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Bone.cpp" />
    <ClCompile Include="Cheats.cpp" />
    <ClCompile Include="CheatsThread.cpp" />
    <ClCompile Include="Entity.cpp" />
    <ClCompile Include="Game.cpp" />
    <ClCompile Include="GlobalVars.cpp" />
    <ClCompile Include="KMbox\KmboxB.cpp" />
    <ClCompile Include="KMbox\KmBoxNetManager.cpp" />
    <ClCompile Include="main.cpp" />
    <ClCompile Include="Offsets.cpp" />
    <ClCompile Include="OS-ImGui\imgui\imgui.cpp" />
    <ClCompile Include="OS-ImGui\imgui\imgui_demo.cpp" />
    <ClCompile Include="OS-ImGui\imgui\imgui_draw.cpp" />
    <ClCompile Include="OS-ImGui\imgui\imgui_impl_dx11.cpp" />
    <ClCompile Include="OS-ImGui\imgui\imgui_impl_win32.cpp" />
    <ClCompile Include="OS-ImGui\imgui\imgui_tables.cpp" />
    <ClCompile Include="OS-ImGui\imgui\imgui_widgets.cpp" />
    <ClCompile Include="OS-ImGui\OS-ImGui.cpp" />
    <ClCompile Include="OS-ImGui\OS-ImGui_Base.cpp" />
    <ClCompile Include="OS-ImGui\OS-ImGui_External.cpp" />
    <ClCompile Include="Radar\Radar.cpp" />
    <ClCompile Include="TriggerBot.cpp" />
    <ClCompile Include="Utils\ConfigMenu.cpp" />
    <ClCompile Include="Utils\ConfigSaver.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>